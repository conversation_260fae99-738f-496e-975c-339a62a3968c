# 常量定义最佳实践

## 🎯 **核心原则**

**常量应该放在最接近其业务逻辑的地方，而不是最先使用它的地方。**

## 📁 **推荐的常量组织结构**

### 1. **Entity层常量** (推荐 ✅)
```go
// api/entity/filesearch_constants.go
package entity

var (
    FileSugDefaultBox = "总结下这篇文档的核心内容"
    FileSugWords = []string{
        "用关键词总结这篇文档",
        "帮我扩写这篇文档",
        // ...
    }
)
```

**适用场景**:
- 业务实体相关的常量
- 多层都需要使用的常量
- 业务规则和配置

### 2. **Service层常量**
```go
// api/service/bos.go
const (
    BucketName = "rec-ui"
    EndPoint = "https://bj.bcebos.com"
)
```

**适用场景**:
- 服务特定的配置常量
- 技术实现相关的常量
- 只在service层使用的常量

### 3. **Handler层常量** (谨慎使用 ⚠️)
```go
// api/handler/const.go
const (
    DefaultPageSize = 20
    MaxPageSize = 100
)
```

**适用场景**:
- HTTP相关的常量 (状态码、默认值等)
- 只在handler层使用的常量

## 🚫 **避免的反模式**

### 1. **在Handler中定义业务常量**
```go
// ❌ 不推荐
// api/handler/const.go
var FileSugWords = []string{"业务相关词条"}
```

**问题**:
- 业务逻辑不应该在handler层定义
- 其他层无法复用
- 违反分层架构原则

### 2. **重复导出常量**
```go
// ❌ 不推荐
// api/handler/const.go
var FileSugWords = entity.FileSugWords // 不必要的重新导出
```

**问题**:
- 增加了不必要的中间层
- 可能造成循环依赖
- 维护成本增加

## ✅ **最佳实践**

### 1. **直接使用Entity常量**
```go
// ✅ 推荐
// api/service/filesearch_business.go
func (s *service) determineSuggestionWords(files []entity.FileItem) ([]string, string) {
    switch files[0].Type {
    case "file":
        return entity.FileSugWords, entity.FileSugDefaultBox
    case "image":
        return entity.ImageSugWords, entity.ImageSugDefaultBox
    }
}
```

### 2. **按业务域组织常量**
```go
// ✅ 推荐
// api/entity/filesearch_constants.go - 文件搜索相关
// api/entity/user_constants.go - 用户相关
// api/entity/payment_constants.go - 支付相关
```

### 3. **使用有意义的包结构**
```
api/
├── entity/           # 业务实体和常量
│   ├── filesearch_constants.go
│   ├── filesearch_types.go
│   └── user_constants.go
├── service/          # 业务逻辑和服务特定常量
│   ├── filesearch_business.go
│   └── bos.go (包含BOS相关常量)
└── handler/          # HTTP处理和HTTP相关常量
    └── filesearch.go
```

## 🔄 **迁移策略**

### 当前项目的改进
1. ✅ **已完成**: 将业务常量移动到 `api/entity/filesearch_constants.go`
2. ✅ **已完成**: Service层直接使用entity常量
3. ✅ **已完成**: 移除handler层的常量重新导出
4. 📝 **建议**: 在handler/const.go中添加迁移说明

### 对于现有代码
```go
// 旧代码
import "api/handler"
words := handler.FileSugWords

// 新代码 (推荐)
import "api/entity"
words := entity.FileSugWords
```

## 📊 **依赖关系图**

```
正确的依赖方向:
Handler -> Entity ✅
Service -> Entity ✅
Model -> Entity ✅

错误的依赖方向:
Entity -> Handler ❌
Entity -> Service ❌
```

## 🎯 **总结**

### 为什么Entity更适合放常量？

1. **业务归属**: 常量是业务实体的一部分
2. **复用性**: 可以被多个层使用
3. **依赖方向**: 符合正确的依赖关系
4. **职责清晰**: Entity负责定义业务实体和相关常量
5. **维护性**: 业务变更时只需修改一个地方

### 关键原则
- 🎯 **就近原则**: 常量放在最接近其业务逻辑的地方
- 🔄 **复用原则**: 多层使用的常量放在共同依赖的层
- 📐 **分层原则**: 遵循正确的依赖方向
- 🧹 **简洁原则**: 避免不必要的重新导出

通过将常量放在entity层，我们实现了更清晰的架构和更好的代码组织。
